@echo off
echo Compiling dump_features...
echo.
echo Checking files...
if exist dump_features.c echo dump_features.c found
if exist denoise.c echo denoise.c found
if exist kiss_fft.c echo kiss_fft.c found
if exist pitch.c echo pitch.c found
if exist celt_lpc.c echo celt_lpc.c found
echo.
echo Running compilation...
gcc -DTRAINING=1 -I../include -I. -v -o dump_features.exe dump_features.c denoise.c kiss_fft.c pitch.c celt_lpc.c -lm 2>&1
echo.
echo Checking result...
if exist dump_features.exe (
    echo Compilation successful! dump_features.exe created.
    echo File size:
    dir dump_features.exe
) else (
    echo Compilation failed! No executable created.
)
pause
