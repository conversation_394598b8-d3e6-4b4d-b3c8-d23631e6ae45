@echo off
echo Simple compilation attempt...
gcc -DTRAINING=1 -I../include -I. -o dump_features.exe dump_features.c denoise.c kiss_fft.c pitch.c celt_lpc.c -lm
if exist dump_features.exe (
    echo Success! dump_features.exe created.
    echo.
    echo Usage: dump_features.exe speech.pcm noise.pcm fg_noise.pcm output.f32 count
    echo.
    echo Example with your data:
    echo dump_features.exe ../data/clean_PCM_data/000_001.pcm ../data/wind_PCM_data/000_001.pcm ../data/wind_PCM_data/000_002.pcm features.f32 100
) else (
    echo Failed to create executable.
)
