@echo off
echo Building dump_features...
echo.

REM 编译 dump_features
gcc -DTRAINING=1 -I../include -I. dump_features.c denoise.c kiss_fft.c pitch.c celt_lpc.c -o dump_features.exe -lm

REM 检查是否编译成功
if exist dump_features.exe (
    echo SUCCESS: dump_features.exe created!
    echo.
    echo File info:
    dir dump_features.exe
    echo.
    echo Testing dump_features usage:
    dump_features.exe
    echo.
    echo Usage: dump_features.exe speech.pcm noise.pcm fg_noise.pcm output.f32 count
    echo.
    echo Example with your data:
    echo dump_features.exe ..\data\clean_PCM_data\000_001.pcm ..\data\wind_PCM_data\000_001.pcm ..\data\wind_PCM_data\000_002.pcm features.f32 100
) else (
    echo FAILED: dump_features.exe not created
    echo.
    echo Trying simple test compilation...
    gcc test_compile.c -o test.exe
    if exist test.exe (
        echo Simple compilation works
        test.exe
        del test.exe
    ) else (
        echo Even simple compilation fails
    )
)

pause
